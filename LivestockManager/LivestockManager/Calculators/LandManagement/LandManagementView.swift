import SwiftUI

// 土地管理视图
struct LandManagementView: View {
    @State private var landArea: Double = 10
    @State private var selectedAreaUnit: AreaUnit = .mu
    @State private var selectedResourceType: ResourceType = .fertilizer
    @State private var resourceAmount: Double = 100
    @State private var resourceUnit: String = ""
    @State private var calculationResult: Double = 0
    @State private var selectedFertilizerType: FertilizerType = .compound
    @State private var selectedSeedType: SeedType = .corn
    
    enum AreaUnit: String, CaseIterable, Identifiable {
        case mu = "mu"
        case hectare = "hectare"
        case acre = "acre"

        var id: String { self.rawValue }

        var displayName: String {
            switch self {
            case .mu: return L.Tools.Calculator.Land.mu
            case .hectare: return L.Tools.Calculator.Land.hectare
            case .acre: return L.Tools.Calculator.Land.acre
            }
        }
        
        var conversionToSquareMeter: Double {
            switch self {
            case .mu: return 666.7
            case .hectare: return 10000
            case .acre: return 4046.86
            }
        }
    }
    
    enum ResourceType: String, CaseIterable, Identifiable {
        case fertilizer = "fertilizer"
        case seed = "seed"
        case water = "water"

        var id: String { self.rawValue }

        var displayName: String {
            switch self {
            case .fertilizer: return L.Tools.Calculator.fertilizer
            case .seed: return L.Tools.Calculator.seed
            case .water: return L.Tools.Calculator.water
            }
        }

        var unit: String {
            switch self {
            case .fertilizer: return L.Tools.Calculator.Land.kg
            case .seed: return L.Tools.Calculator.Land.kg
            case .water: return L.Tools.Calculator.Land.cubicMeter
            }
        }
    }
    
    enum FertilizerType: String, CaseIterable, Identifiable {
        case nitrogen = "nitrogen"
        case phosphorus = "phosphorus"
        case potassium = "potassium"
        case compound = "compound"
        case organic = "organic"

        var id: String { self.rawValue }

        var displayName: String {
            switch self {
            case .nitrogen: return L.Tools.Calculator.Land.nitrogen
            case .phosphorus: return L.Tools.Calculator.Land.phosphorus
            case .potassium: return L.Tools.Calculator.Land.potassium
            case .compound: return L.Tools.Calculator.Land.compound
            case .organic: return L.Tools.Calculator.Land.organic
            }
        }
        
        var recommendedRate: ClosedRange<Double> {
            switch self {
            case .nitrogen: return 15...30
            case .phosphorus: return 20...40
            case .potassium: return 15...30
            case .compound: return 30...60
            case .organic: return 100...300
            }
        }
    }
    
    enum SeedType: String, CaseIterable, Identifiable {
        case corn = "corn"
        case wheat = "wheat"
        case rice = "rice"
        case soybean = "soybean"
        case alfalfa = "alfalfa"

        var id: String { self.rawValue }

        var displayName: String {
            switch self {
            case .corn: return L.Tools.Calculator.Land.corn
            case .wheat: return L.Tools.Calculator.Land.wheat
            case .rice: return L.Tools.Calculator.Land.rice
            case .soybean: return L.Tools.Calculator.Land.soybean
            case .alfalfa: return L.Tools.Calculator.Land.alfalfa
            }
        }
        
        var recommendedRate: ClosedRange<Double> {
            switch self {
            case .corn: return 20...30
            case .wheat: return 100...150
            case .rice: return 10...15
            case .soybean: return 60...80
            case .alfalfa: return 15...25
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 24) {
            // 标题和说明
            VStack(alignment: .leading, spacing: 8) {
                Text(L.Tools.Calculator.landTitle)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.black)

                Text(L.Tools.Calculator.landDescription)
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.6))
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 土地面积输入卡片
            VStack(spacing: 20) {
                Text(L.Tools.Calculator.landArea)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)

                HStack {
                    TextField(L.Tools.Calculator.landArea, value: $landArea, formatter: NumberFormatter())
                        .keyboardType(.decimalPad)
                        .padding()
                        .background(Color.white)
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        .onChange(of: landArea) { _ in
                            calculateResource()
                        }

                    Picker(L.Tools.Calculator.areaUnit, selection: $selectedAreaUnit) {
                        ForEach(AreaUnit.allCases) { unit in
                            Text(unit.displayName).tag(unit)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())
                    .frame(width: 100)
                    .onChange(of: selectedAreaUnit) { _ in
                        calculateResource()
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 资源类型选择卡片
            VStack(spacing: 20) {
                Text(L.Tools.Calculator.resourceType)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)
                
                HStack(spacing: 10) {
                    ForEach(ResourceType.allCases) { type in
                        Button(action: {
                            selectedResourceType = type
                            resourceUnit = type.unit
                            calculateResource()
                        }) {
                            Text(type.displayName)
                                .font(.system(size: 14, weight: selectedResourceType == type ? .semibold : .regular))
                                .foregroundColor(selectedResourceType == type ? .white : .black)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(selectedResourceType == type ? Color.black : Color.white)
                                .cornerRadius(20)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 20)
                                        .stroke(Color.black.opacity(0.1), lineWidth: selectedResourceType == type ? 0 : 1)
                                )
                        }
                    }
                }
                
                // 肥料类型选择（仅当选择肥料时显示）
                if selectedResourceType == .fertilizer {
                    VStack(alignment: .leading, spacing: 12) {
                        Text(L.Tools.Calculator.fertilizerType)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.black.opacity(0.7))

                        Picker(L.Tools.Calculator.fertilizerType, selection: $selectedFertilizerType) {
                            ForEach(FertilizerType.allCases) { type in
                                Text(type.displayName).tag(type)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: selectedFertilizerType) { _ in
                            calculateResource()
                        }
                    }
                }

                // 种子类型选择（仅当选择种子时显示）
                if selectedResourceType == .seed {
                    VStack(alignment: .leading, spacing: 12) {
                        Text(L.Tools.Calculator.cropType)
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(Color.black.opacity(0.7))

                        Picker(L.Tools.Calculator.cropType, selection: $selectedSeedType) {
                            ForEach(SeedType.allCases) { type in
                                Text(type.displayName).tag(type)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: selectedSeedType) { _ in
                            calculateResource()
                        }
                    }
                }
                
                // 资源总量输入
                VStack(alignment: .leading, spacing: 12) {
                    Text(L.Tools.Calculator.totalAmount)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(Color.black.opacity(0.7))

                    HStack {
                        TextField(L.Tools.Calculator.totalAmount, value: $resourceAmount, formatter: NumberFormatter())
                            .keyboardType(.decimalPad)
                            .padding()
                            .background(Color.white)
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
                            )
                            .onChange(of: resourceAmount) { _ in
                                calculateResource()
                            }

                        Text(resourceUnit)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))
                            .frame(width: 60)
                    }
                }
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 计算结果卡片
            VStack(spacing: 20) {
                Text(L.Tools.Calculator.calculationResult)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)
                    .frame(maxWidth: .infinity, alignment: .leading)

                VStack(spacing: 16) {
                    // 每单位面积的资源用量
                    HStack {
                        Text(String(format: L.Tools.Calculator.perUnitUsage, selectedAreaUnit.displayName) + ":")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))

                        Spacer()

                        Text(String(format: "%.2f %@", calculationResult, resourceUnit))
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.black)
                    }

                    // 推荐用量（仅在适用时显示）
                    if let recommendedRange = getRecommendedRange() {
                        HStack {
                            Text(L.Tools.Calculator.recommendedRange + ":")
                                .font(.system(size: 14))
                                .foregroundColor(Color.black.opacity(0.7))

                            Spacer()

                            Text(String(format: "%.1f - %.1f %@", recommendedRange.lowerBound, recommendedRange.upperBound, resourceUnit))
                                .font(.system(size: 14))
                                .foregroundColor(isWithinRecommendedRange() ? .green : .orange)
                        }

                        // 如果计算结果不在推荐范围内，显示提示
                        if !isWithinRecommendedRange() {
                            Text(calculationResult < recommendedRange.lowerBound ? L.Tools.Calculator.lowUsageWarning : L.Tools.Calculator.highUsageWarning)
                                .font(.system(size: 14))
                                .foregroundColor(.orange)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(.top, 4)
                        }
                    }

                    // 总覆盖面积（当输入资源总量时）
                    HStack {
                        Text(L.Tools.Calculator.totalCoverage + ":")
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.7))

                        Spacer()

                        Text(String(format: "%.2f %@", resourceAmount / calculationResult, selectedAreaUnit.displayName))
                            .font(.system(size: 14))
                            .foregroundColor(.black)
                    }
                }
                .padding(.vertical, 8)
            }
            .padding(16)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
            
            // 使用建议卡片
            VStack(alignment: .leading, spacing: 12) {
                Text(L.Tools.Calculator.usageAdvice)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.black)

                Text(getUsageAdvice())
                    .font(.system(size: 14))
                    .foregroundColor(Color.black.opacity(0.7))
                    .lineSpacing(4)
            }
            .padding(16)
            .frame(maxWidth: .infinity, alignment: .leading)
            .background(Color.white)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.black.opacity(0.08), lineWidth: 1)
            )
        }
        .onAppear {
            resourceUnit = selectedResourceType.unit
            calculateResource()
        }
    }
    
    // 计算每单位面积的资源使用量
    private func calculateResource() {
        if landArea > 0 {
            calculationResult = resourceAmount / landArea
        } else {
            calculationResult = 0
        }
    }
    
    // 获取推荐用量范围
    private func getRecommendedRange() -> ClosedRange<Double>? {
        switch selectedResourceType {
        case .fertilizer:
            return selectedFertilizerType.recommendedRate
        case .seed:
            return selectedSeedType.recommendedRate
        case .water:
            // 水的推荐灌溉量根据不同的作物和土壤类型有很大差异
            // 这里提供一个非常粗略的估计
            return 30...60
        }
    }
    
    // 检查计算结果是否在推荐范围内
    private func isWithinRecommendedRange() -> Bool {
        if let range = getRecommendedRange() {
            return range.contains(calculationResult)
        }
        return true
    }
    
    // 获取使用建议
    private func getUsageAdvice() -> String {
        switch selectedResourceType {
        case .fertilizer:
            switch selectedFertilizerType {
            case .nitrogen:
                return L.Tools.Calculator.Land.Advice.nitrogen
            case .phosphorus:
                return L.Tools.Calculator.Land.Advice.phosphorus
            case .potassium:
                return L.Tools.Calculator.Land.Advice.potassium
            case .compound:
                return L.Tools.Calculator.Land.Advice.compound
            case .organic:
                return L.Tools.Calculator.Land.Advice.organic
            }
        case .seed:
            switch selectedSeedType {
            case .corn:
                return L.Tools.Calculator.Land.Advice.corn
            case .wheat:
                return L.Tools.Calculator.Land.Advice.wheat
            case .rice:
                return L.Tools.Calculator.Land.Advice.rice
            case .soybean:
                return L.Tools.Calculator.Land.Advice.soybean
            case .alfalfa:
                return L.Tools.Calculator.Land.Advice.alfalfa
            }
        case .water:
            return L.Tools.Calculator.Land.Advice.water
        }
    }
} 